package com.airdoc.research.service.impl;

import com.airdoc.research.dto.MovementTestSubmitDTO;
import com.airdoc.research.entity.*;
import com.airdoc.research.mapper.*;
import com.airdoc.research.service.MovementTestService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * Movement检测服务实现类（使用@Resource注解版本）
 * 展示Spring Boot 3推荐的@Resource依赖注入方式
 */
@Slf4j
@Service("movementTestServiceResourceImpl")
public class MovementTestServiceResourceImpl implements MovementTestService {

    // 使用@Resource注解进行依赖注入（JSR-250标准，Spring Boot 3推荐）
    @Resource
    private EmPatientMapper patientMapper;

    @Resource
    private SysUserMapper userMapper;

    @Resource
    private SysDeviceMapper deviceMapper;

    @Resource
    private EmTestRecordMapper testRecordMapper;

    @Resource
    private GazeTrajectoryDataMapper trajectoryDataMapper;

    @Resource
    private GazeStabilityResultMapper gazeStabilityResultMapper;

    @Resource
    private FollowAbilityResultMapper followAbilityResultMapper;

    @Resource
    private SaccadeAbilityResultMapper saccadeAbilityResultMapper;

    @Resource
    private RoiDetectionResultMapper roiDetectionResultMapper;

    @Resource
    private ObjectMapper objectMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long submitTestResult(MovementTestSubmitDTO submitDTO, String deviceSn) {
        log.info("开始处理Movement检测结果提交（@Resource版本），检测类型：{}", submitDTO.getTestInfo().getTestType());

        try {
            // 1. 获取患者ID
            Long patientId = submitDTO.getPatientId();

            // 2. 处理设备信息（通过设备序列号）
            Long deviceId = handleDeviceInfo(deviceSn);

            // 3. 创建检测记录（不需要操作员ID）
            Long recordId = createTestRecord(submitDTO.getTestInfo(), patientId, null, deviceId);

            // 5. 保存视线轨迹数据
            saveGazeTrajectoryData(recordId, submitDTO.getResultData().getGazeTrajectory());

            // 6. 根据检测类型保存对应的结果数据
            saveTestResultData(recordId, submitDTO.getTestInfo().getTestType(), submitDTO.getResultData());

            log.info("Movement检测结果提交成功（@Resource版本），记录ID：{}", recordId);
            return recordId;

        } catch (Exception e) {
            log.error("Movement检测结果提交失败（@Resource版本）", e);
            throw new RuntimeException("检测结果提交失败：" + e.getMessage());
        }
    }





    /**
     * 处理设备信息
     */
    private Long handleDeviceInfo(String deviceSn) {
        // 根据设备序列号查询设备
        SysDevice existingDevice = deviceMapper.selectOne(
            new LambdaQueryWrapper<SysDevice>()
                .eq(SysDevice::getDeviceSn, deviceSn)
        );

        if (existingDevice != null) {
            // 设备已存在，直接返回ID
            return existingDevice.getId();
        } else {
            // 创建新设备（只有基本信息）
            SysDevice newDevice = new SysDevice();
            newDevice.setDeviceSn(deviceSn);
            newDevice.setDeviceStatus("ONLINE");

            deviceMapper.insert(newDevice);
            return newDevice.getId();
        }
    }

    /**
     * 创建检测记录
     */
    private Long createTestRecord(MovementTestSubmitDTO.TestInfo testInfo, Long patientId, Long operatorId, Long deviceId) {
        EmTestRecord testRecord = new EmTestRecord();
        BeanUtils.copyProperties(testInfo, testRecord);

        testRecord.setPatientId(patientId);
        testRecord.setOperatorId(operatorId);
        testRecord.setDeviceId(deviceId);
        testRecord.setStatus("COMPLETED");

        // 设置图片URL（如果提供了的话）
        if (testInfo.getImageUrl() != null && !testInfo.getImageUrl().trim().isEmpty()) {
            testRecord.setImageUrl(testInfo.getImageUrl());
            log.info("设置检测记录图片URL：{}", testInfo.getImageUrl());
        }

        testRecordMapper.insert(testRecord);
        return testRecord.getId();
    }

    /**
     * 保存视线轨迹数据
     */
    private void saveGazeTrajectoryData(Long recordId, List<MovementTestSubmitDTO.GazePoint> gazeTrajectory) {
        if (gazeTrajectory == null || gazeTrajectory.isEmpty()) {
            return;
        }

        for (MovementTestSubmitDTO.GazePoint gazePoint : gazeTrajectory) {
            GazeTrajectoryData trajectoryData = new GazeTrajectoryData();
            BeanUtils.copyProperties(gazePoint, trajectoryData);
            trajectoryData.setRecordId(recordId);

            // 将原始数据转换为JSON
            try {
                trajectoryData.setRawData(objectMapper.writeValueAsString(gazePoint));
            } catch (JsonProcessingException e) {
                log.warn("视线轨迹数据JSON转换失败", e);
            }

            trajectoryDataMapper.insert(trajectoryData);
        }
    }

    /**
     * 根据检测类型保存对应的结果数据
     */
    private void saveTestResultData(Long recordId, String testType, MovementTestSubmitDTO.TestResultData resultData) {
        switch (testType) {
            case "GAZE_STABILITY":
                saveGazeStabilityResult(recordId, resultData.getGazeStabilityData());
                break;
            case "FOLLOW_ABILITY":
                saveFollowAbilityResult(recordId, resultData.getFollowAbilityData());
                break;
            case "SACCADE_ABILITY":
                saveSaccadeAbilityResult(recordId, resultData.getSaccadeAbilityData());
                break;
            case "ROI_DETECTION":
                saveRoiDetectionResult(recordId, resultData.getRoiDetectionData());
                break;
            default:
                log.warn("未知的检测类型：{}", testType);
        }
    }

    /**
     * 保存注视稳定性结果
     */
    private void saveGazeStabilityResult(Long recordId, MovementTestSubmitDTO.GazeStabilityData data) {
        if (data == null) return;

        GazeStabilityResult result = new GazeStabilityResult();
        BeanUtils.copyProperties(data, result);
        result.setRecordId(recordId);

        gazeStabilityResultMapper.insert(result);
    }

    /**
     * 保存追随能力结果
     */
    private void saveFollowAbilityResult(Long recordId, MovementTestSubmitDTO.FollowAbilityData data) {
        if (data == null) return;

        FollowAbilityResult result = new FollowAbilityResult();
        BeanUtils.copyProperties(data, result);
        result.setRecordId(recordId);

        followAbilityResultMapper.insert(result);
    }

    /**
     * 保存扫视能力结果
     */
    private void saveSaccadeAbilityResult(Long recordId, MovementTestSubmitDTO.SaccadeAbilityData data) {
        if (data == null) return;

        SaccadeAbilityResult result = new SaccadeAbilityResult();
        BeanUtils.copyProperties(data, result);
        result.setRecordId(recordId);

        saccadeAbilityResultMapper.insert(result);
    }

    /**
     * 保存ROI检测结果
     */
    private void saveRoiDetectionResult(Long recordId, MovementTestSubmitDTO.RoiDetectionData data) {
        if (data == null) return;

        RoiDetectionResult result = new RoiDetectionResult();
        BeanUtils.copyProperties(data, result);
        result.setRecordId(recordId);

        roiDetectionResultMapper.insert(result);
    }
}
