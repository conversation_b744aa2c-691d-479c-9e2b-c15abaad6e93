package com.airdoc.research.controller;

import com.airdoc.research.common.Result;
import com.airdoc.research.dto.MovementTestSubmitDTO;
import com.airdoc.research.service.MovementTestService;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;



/**
 * Movement检测结果提交控制器
 * 使用@Resource注解进行依赖注入（Spring Boot 3推荐方式）
 */
@Slf4j
@RestController
@RequestMapping("/movement")
@Validated
public class MovementTestController {

    // 使用@Resource注解进行依赖注入（JSR-250标准，Spring Boot 3推荐）
    @Resource
    private MovementTestService movementTestService;

    /**
     * 提交注视稳定性检测结果
     * @param submitDTO 检测结果数据
     * @return 提交结果
     */
    @PostMapping("/gaze-stability/submit")
    public Result<Long> submitGazeStabilityResult(@Valid @RequestBody MovementTestSubmitDTO submitDTO,
                                                  @RequestHeader("X-Device-Sn") String deviceSn) {
        log.info("接收到注视稳定性检测结果提交请求");
        
        try {
            // 验证检测类型
            if (!"GAZE_STABILITY".equals(submitDTO.getTestInfo().getTestType())) {
                return Result.badRequest("检测类型不匹配，期望：GAZE_STABILITY");
            }
            
            if (!"01".equals(submitDTO.getTestInfo().getTestSequence())) {
                return Result.badRequest("检测序列号不匹配，期望：01");
            }
            
            Long recordId = movementTestService.submitTestResult(submitDTO, deviceSn);
            return Result.success("注视稳定性检测结果提交成功", recordId);
            
        } catch (Exception e) {
            log.error("注视稳定性检测结果提交失败", e);
            return Result.error("提交失败：" + e.getMessage());
        }
    }

    /**
     * 提交追随能力检测结果
     * @param submitDTO 检测结果数据
     * @return 提交结果
     */
    @PostMapping("/follow-ability/submit")
    public Result<Long> submitFollowAbilityResult(@Valid @RequestBody MovementTestSubmitDTO submitDTO,
                                                  @RequestHeader("X-Device-Sn") String deviceSn) {
        log.info("接收到追随能力检测结果提交请求");
        
        try {
            // 验证检测类型
            if (!"FOLLOW_ABILITY".equals(submitDTO.getTestInfo().getTestType())) {
                return Result.badRequest("检测类型不匹配，期望：FOLLOW_ABILITY");
            }
            
            if (!"02".equals(submitDTO.getTestInfo().getTestSequence())) {
                return Result.badRequest("检测序列号不匹配，期望：02");
            }
            
            Long recordId = movementTestService.submitTestResult(submitDTO, deviceSn);
            return Result.success("追随能力检测结果提交成功", recordId);
            
        } catch (Exception e) {
            log.error("追随能力检测结果提交失败", e);
            return Result.error("提交失败：" + e.getMessage());
        }
    }

    /**
     * 提交扫视能力检测结果
     * @param submitDTO 检测结果数据
     * @return 提交结果
     */
    @PostMapping("/saccade-ability/submit")
    public Result<Long> submitSaccadeAbilityResult(@Valid @RequestBody MovementTestSubmitDTO submitDTO,
                                                   @RequestHeader("X-Device-Sn") String deviceSn) {
        log.info("接收到扫视能力检测结果提交请求");
        
        try {
            // 验证检测类型
            if (!"SACCADE_ABILITY".equals(submitDTO.getTestInfo().getTestType())) {
                return Result.badRequest("检测类型不匹配，期望：SACCADE_ABILITY");
            }
            
            if (!"03".equals(submitDTO.getTestInfo().getTestSequence())) {
                return Result.badRequest("检测序列号不匹配，期望：03");
            }
            
            Long recordId = movementTestService.submitTestResult(submitDTO, deviceSn);
            return Result.success("扫视能力检测结果提交成功", recordId);
            
        } catch (Exception e) {
            log.error("扫视能力检测结果提交失败", e);
            return Result.error("提交失败：" + e.getMessage());
        }
    }

    /**
     * 提交ROI检测结果
     * @param submitDTO 检测结果数据
     * @return 提交结果
     */
    @PostMapping("/roi-detection/submit")
    public Result<Long> submitRoiDetectionResult(@Valid @RequestBody MovementTestSubmitDTO submitDTO,
                                                 @RequestHeader("X-Device-Sn") String deviceSn) {
        log.info("接收到ROI检测结果提交请求");
        
        try {
            // 验证检测类型
            if (!"ROI_DETECTION".equals(submitDTO.getTestInfo().getTestType())) {
                return Result.badRequest("检测类型不匹配，期望：ROI_DETECTION");
            }
            
            if (!"04".equals(submitDTO.getTestInfo().getTestSequence())) {
                return Result.badRequest("检测序列号不匹配，期望：04");
            }
            
            Long recordId = movementTestService.submitTestResult(submitDTO, deviceSn);
            return Result.success("ROI检测结果提交成功", recordId);
            
        } catch (Exception e) {
            log.error("ROI检测结果提交失败", e);
            return Result.error("提交失败：" + e.getMessage());
        }
    }

    /**
     * 通用提交接口（支持所有检测类型）
     * @param submitDTO 检测结果数据
     * @return 提交结果
     */
    @PostMapping("/submit")
    public Result<Long> submitTestResult(@Valid @RequestBody MovementTestSubmitDTO submitDTO,
                                         @RequestHeader("X-Device-Sn") String deviceSn) {
        log.info("接收到Movement检测结果提交请求，类型：{}", submitDTO.getTestInfo().getTestType());
        
        try {
            Long recordId = movementTestService.submitTestResult(submitDTO, deviceSn);
            return Result.success("检测结果提交成功", recordId);
            
        } catch (Exception e) {
            log.error("检测结果提交失败", e);
            return Result.error("提交失败：" + e.getMessage());
        }
    }

    /**
     * 健康检查接口
     * @return 健康状态
     */
    @GetMapping("/health")
    public Result<String> health() {
        return Result.success("Movement检测服务运行正常");
    }
}
