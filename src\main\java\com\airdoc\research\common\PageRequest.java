package com.airdoc.research.common;

import lombok.Data;

/**
 * 分页请求参数
 */
@Data
public class PageRequest {

    /**
     * 当前页码，从1开始
     */
    private Integer current = 1;

    /**
     * 每页大小
     */
    private Integer size = 10;

    /**
     * 排序字段
     */
    private String sortField;

    /**
     * 排序方向 ASC/DESC
     */
    private String sortOrder = "DESC";

    /**
     * 获取偏移量
     */
    public long getOffset() {
        return (long) (current - 1) * size;
    }

    /**
     * 验证并修正分页参数
     */
    public void validate() {
        if (current == null || current < 1) {
            current = 1;
        }
        if (size == null || size < 1) {
            size = 10;
        }
        if (size > 100) {
            size = 100; // 限制最大页面大小
        }
        if (sortOrder == null || (!sortOrder.equalsIgnoreCase("ASC") && !sortOrder.equalsIgnoreCase("DESC"))) {
            sortOrder = "DESC";
        }
    }
}
